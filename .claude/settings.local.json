{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "WebFetch(domain:mcp.sentry.dev)", "WebFetch(domain:github.com)", "Bash(claude mcp add:*)", "<PERSON><PERSON>(claude mcp:*)", "Bash(npx @magicuidesign/cli@latest install:*)", "mcp__serena__initial_instructions", "Bash(find:*)", "Bash(ls:*)", "mcp__filesystem__read_file", "Bash(claude code config --help)", "<PERSON><PERSON>(mkdir:*)", "mcp__serena__think_about_task_adherence", "mcp__serena__think_about_whether_you_are_done", "Bash(npm install:*)", "mcp__sequential-thinking__sequentialthinking", "mcp__serena__get_symbols_overview", "mcp__serena__list_dir", "mcp__serena__list_memories", "mcp__filesystem__read_multiple_files", "mcp__serena__write_memory", "mcp__filesystem__directory_tree", "<PERSON><PERSON>(mv:*)", "Bash(rm:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)", "mcp__serena__check_onboarding_performed", "mcp__serena__find_file", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "Bash(npm audit:*)", "mcp__serena__think_about_collected_information", "mcp__serena__read_memory", "Bash(npm run test:cov:*)", "mcp__filesystem__list_directory", "mcp__filesystem__search_files", "Bash(grep:*)", "Bash(/tmp/update_navigation_hooks.sh:*)", "mcp__serena__activate_project", "Bash(pnpm build:*)", "Bash(npm run build:web:*)", "Bash(npm run build:*)", "Bash(pnpm install:*)", "Bash(cp:*)", "Bash(pnpm tsr:*)", "Bash(npx tsr:*)", "Bash(npx tsc:*)", "Bash(npx vite build:*)", "mcp__filesystem__list_allowed_directories", "Bash(npm ls:*)", "Bash(npm cache clean:*)", "Bash(npm test)", "Bash(npm test:*)", "Bash(npm run test:run:*)", "Bash(npm run dev:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(true)", "Bash(npx @tanstack/router-cli:*)", "mcp__serena__get_current_config", "Bash(npm uninstall:*)", "mcp__memory__create_entities", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npx prisma migrate:*)", "Bash(npm run prisma:migrate:dev:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(npx prisma:*)", "Bash(npm run start:dev:*)", "Bash(timeout 30 npm run start:dev)", "mcp__serena__replace_regex", "mcp__serena__replace_symbol_body"], "deny": []}}