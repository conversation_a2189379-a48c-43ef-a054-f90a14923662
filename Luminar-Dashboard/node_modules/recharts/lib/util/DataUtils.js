"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.compareValues = void 0;
exports.findEntryInArray = findEntryInArray;
exports.uniqueId = exports.mathSign = exports.isPercent = exports.isNumber = exports.isNumOrStr = exports.isNullish = exports.interpolateNumber = exports.hasDuplicate = exports.getPercentValue = exports.getLinearRegression = exports.getAnyElementOfObject = void 0;
var _isString = _interopRequireDefault(require("lodash/isString"));
var _isNaN = _interopRequireDefault(require("lodash/isNaN"));
var _get = _interopRequireDefault(require("lodash/get"));
var _isNumber = _interopRequireDefault(require("lodash/isNumber"));
var _isNil = _interopRequireDefault(require("lodash/isNil"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }
var mathSign = exports.mathSign = function mathSign(value) {
  if (value === 0) {
    return 0;
  }
  if (value > 0) {
    return 1;
  }
  return -1;
};
var isPercent = exports.isPercent = function isPercent(value) {
  return (0, _isString["default"])(value) && value.indexOf('%') === value.length - 1;
};
var isNumber = exports.isNumber = function isNumber(value) {
  return (0, _isNumber["default"])(value) && !(0, _isNaN["default"])(value);
};
var isNullish = exports.isNullish = function isNullish(value) {
  return (0, _isNil["default"])(value);
};
var isNumOrStr = exports.isNumOrStr = function isNumOrStr(value) {
  return isNumber(value) || (0, _isString["default"])(value);
};
var idCounter = 0;
var uniqueId = exports.uniqueId = function uniqueId(prefix) {
  var id = ++idCounter;
  return "".concat(prefix || '').concat(id);
};

/**
 * Get percent value of a total value
 * @param {number|string} percent A percent
 * @param {number} totalValue     Total value
 * @param {number} defaultValue   The value returned when percent is undefined or invalid
 * @param {boolean} validate      If set to be true, the result will be validated
 * @return {number} value
 */
var getPercentValue = exports.getPercentValue = function getPercentValue(percent, totalValue) {
  var defaultValue = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
  var validate = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
  if (!isNumber(percent) && !(0, _isString["default"])(percent)) {
    return defaultValue;
  }
  var value;
  if (isPercent(percent)) {
    var index = percent.indexOf('%');
    value = totalValue * parseFloat(percent.slice(0, index)) / 100;
  } else {
    value = +percent;
  }
  if ((0, _isNaN["default"])(value)) {
    value = defaultValue;
  }
  if (validate && value > totalValue) {
    value = totalValue;
  }
  return value;
};
var getAnyElementOfObject = exports.getAnyElementOfObject = function getAnyElementOfObject(obj) {
  if (!obj) {
    return null;
  }
  var keys = Object.keys(obj);
  if (keys && keys.length) {
    return obj[keys[0]];
  }
  return null;
};
var hasDuplicate = exports.hasDuplicate = function hasDuplicate(ary) {
  if (!Array.isArray(ary)) {
    return false;
  }
  var len = ary.length;
  var cache = {};
  for (var i = 0; i < len; i++) {
    if (!cache[ary[i]]) {
      cache[ary[i]] = true;
    } else {
      return true;
    }
  }
  return false;
};

/* @todo consider to rename this function into `getInterpolator` */
var interpolateNumber = exports.interpolateNumber = function interpolateNumber(numberA, numberB) {
  if (isNumber(numberA) && isNumber(numberB)) {
    return function (t) {
      return numberA + t * (numberB - numberA);
    };
  }
  return function () {
    return numberB;
  };
};
function findEntryInArray(ary, specifiedKey, specifiedValue) {
  if (!ary || !ary.length) {
    return null;
  }
  return ary.find(function (entry) {
    return entry && (typeof specifiedKey === 'function' ? specifiedKey(entry) : (0, _get["default"])(entry, specifiedKey)) === specifiedValue;
  });
}

/**
 * The least square linear regression
 * @param {Array} data The array of points
 * @returns {Object} The domain of x, and the parameter of linear function
 */
var getLinearRegression = exports.getLinearRegression = function getLinearRegression(data) {
  if (!data || !data.length) {
    return null;
  }
  var len = data.length;
  var xsum = 0;
  var ysum = 0;
  var xysum = 0;
  var xxsum = 0;
  var xmin = Infinity;
  var xmax = -Infinity;
  var xcurrent = 0;
  var ycurrent = 0;
  for (var i = 0; i < len; i++) {
    xcurrent = data[i].cx || 0;
    ycurrent = data[i].cy || 0;
    xsum += xcurrent;
    ysum += ycurrent;
    xysum += xcurrent * ycurrent;
    xxsum += xcurrent * xcurrent;
    xmin = Math.min(xmin, xcurrent);
    xmax = Math.max(xmax, xcurrent);
  }
  var a = len * xxsum !== xsum * xsum ? (len * xysum - xsum * ysum) / (len * xxsum - xsum * xsum) : 0;
  return {
    xmin: xmin,
    xmax: xmax,
    a: a,
    b: (ysum - a * xsum) / len
  };
};

/**
 * Compare values.
 *
 * This function is intended to be passed to `Array.prototype.sort()`. It properly compares generic homogeneous arrays that are either `string[]`,
 * `number[]`, or `Date[]`. When comparing heterogeneous arrays or homogeneous arrays of other types, it will attempt to compare items properly but
 * will fall back to string comparison for mismatched or unsupported types.
 *
 * For some background, `Array.prototype.sort()`'s default comparator coerces each of the array's items into a string and compares the strings. This
 * often leads to undesirable behavior, especially with numerical items.
 *
 * @param {unknown} a The first item to compare
 * @param {unknown} b The second item to compare
 * @return {number} A negative number if a < b, a positive number if a > b, 0 if equal
 */
var compareValues = exports.compareValues = function compareValues(a, b) {
  if (isNumber(a) && isNumber(b)) {
    return a - b;
  }
  if ((0, _isString["default"])(a) && (0, _isString["default"])(b)) {
    return a.localeCompare(b);
  }
  if (a instanceof Date && b instanceof Date) {
    return a.getTime() - b.getTime();
  }
  return String(a).localeCompare(String(b));
};