## 0.4.5 (Mar 25, 2021)

### chore

- fix: previous release

## 0.4.4 (Mar 18, 2021)

### chore

- bring up all dev deps (incl. eslint and webpack) to the latest
- move CI to Github actions
- add linting and building to ci pipeline (in addition to unit testing)

## 0.4.0 (Seq 28, 2018)

### feat

- use `decimal.js-light` to handle large number or high precision

## 0.3.2 (Aug 21, 2017)

### fix

- fix `getNiceTickValues` when the number is a scientific notation

## 0.3.1 (Jun 11, 2017)

### fix

- fix `getDigitCount` when the number is a scientific notation

## 0.3.0 (Mar 01, 2017)

### feat

- Add new ticks function `getTickValuesFixedDomain`

## 0.2.3 (Feb 28, 2017)

### fix

- Fix calculation precision of calculateStep, add Arithmetic.modulo

## 0.2.2 (Feb 28, 2017)

### fix

- Fix calculation precision of calculateStep

## 0.2.1 (July 25, 2016)

### fix

- Fix the precision of ticks for decimals

## 0.2.0 (July 25, 2016)

### feat

- Support `allowDecimals` option

## 0.1.11 (July 19, 2016)

### fix

- Tweak the strategy of calculating step of ticks

## 0.1.10 (July 07, 2016)

### deps

- update deps and fix lint error

## 0.1.9 (April 08, 2016)

### fix

- Fix ticks for interval [0, 0]

## 0.1.8 (Feb 04, 2016)

### refactor

- Refactor the export method

## 0.1.7 (Feb 04, 2016)

### chore

- Optimize npm script commands
