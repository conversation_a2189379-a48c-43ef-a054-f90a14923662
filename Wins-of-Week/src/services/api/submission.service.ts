import { apiClient } from '@luminar/shared-auth'

export interface WeeklySubmission {
  id: string
  userId: string
  userName?: string
  weekNumber: number
  year: number
  type: 'achievement' | 'cost_initiative' | 'training_idea' | 'recognition'
  title: string
  description: string
  impact?: string
  category?: string
  metrics?: {
    value?: number
    unit?: string
    improvement?: number
  }
  attachments?: string[]
  tags?: string[]
  status: 'draft' | 'submitted' | 'reviewed' | 'published'
  recognitionTo?: {
    userId: string
    userName: string
    department?: string
  }
  createdAt: string
  updatedAt: string
  submittedAt?: string
  reviewedAt?: string
  reviewedBy?: string
}

export interface SubmissionFilters {
  type?: string
  status?: string
  weekNumber?: number
  year?: number
  userId?: string
  search?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface SubmissionStats {
  totalSubmissions: number
  weeklySubmissions: number
  participationRate: number
  topContributors: Array<{
    userId: string
    userName: string
    submissionCount: number
    score: number
  }>
  byType: {
    achievement: number
    cost_initiative: number
    training_idea: number
    recognition: number
  }
}

export class SubmissionService {
  private static BASE_PATH = '/api/v1/submissions'

  // CRUD operations
  static async listSubmissions(filters?: SubmissionFilters): Promise<{ submissions: WeeklySubmission[]; total: number }> {
    const response = await apiClient.get(this.BASE_PATH, { params: filters })
    return response.data
  }

  static async getSubmission(id: string): Promise<WeeklySubmission> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}`)
    return response.data
  }

  static async createSubmission(data: Partial<WeeklySubmission>): Promise<WeeklySubmission> {
    const response = await apiClient.post(this.BASE_PATH, data)
    return response.data
  }

  static async updateSubmission(id: string, data: Partial<WeeklySubmission>): Promise<WeeklySubmission> {
    const response = await apiClient.patch(`${this.BASE_PATH}/${id}`, data)
    return response.data
  }

  static async deleteSubmission(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${id}`)
  }

  // Workflow operations
  static async submitForReview(id: string): Promise<WeeklySubmission> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/submit`)
    return response.data
  }

  static async publishSubmission(id: string): Promise<WeeklySubmission> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/publish`)
    return response.data
  }

  static async rejectSubmission(id: string, reason: string): Promise<WeeklySubmission> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/reject`, { reason })
    return response.data
  }

  // Analytics
  static async getSubmissionStats(weekNumber?: number, year?: number): Promise<SubmissionStats> {
    const response = await apiClient.get(`${this.BASE_PATH}/analytics/stats`, {
      params: { weekNumber, year }
    })
    return response.data
  }

  static async getLeaderboard(period?: 'week' | 'month' | 'quarter' | 'year'): Promise<any> {
    const response = await apiClient.get(`${this.BASE_PATH}/analytics/leaderboard`, {
      params: { period }
    })
    return response.data
  }

  static async getUserSubmissions(userId: string): Promise<WeeklySubmission[]> {
    const response = await apiClient.get(`${this.BASE_PATH}/user/${userId}`)
    return response.data
  }

  // File operations
  static async uploadAttachment(submissionId: string, file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await apiClient.post(`${this.BASE_PATH}/${submissionId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  static async removeAttachment(submissionId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${submissionId}/attachments/${attachmentId}`)
  }

  // Export
  static async exportSubmissions(filters?: SubmissionFilters, format?: 'csv' | 'excel' | 'pdf'): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_PATH}/export`, {
      params: { ...filters, format },
      responseType: 'blob'
    })
    return response.data
  }
}