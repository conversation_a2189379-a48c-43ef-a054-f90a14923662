import { apiClient } from '@luminar/shared-auth'

export interface Proposal {
  id: string
  vendorId: string
  vendorName: string
  title: string
  description: string
  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected'
  amount: number
  currency: string
  validUntil: string
  attachments: string[]
  reviewNotes?: string
  approvedBy?: string
  approvedAt?: string
  createdAt: string
  updatedAt: string
}

export interface ProposalFilters {
  vendorId?: string
  status?: string
  minAmount?: number
  maxAmount?: number
  search?: string
  page?: number
  limit?: number
}

export class ProposalService {
  private static BASE_PATH = '/api/v1/proposals'

  // CRUD operations
  static async listProposals(filters?: ProposalFilters): Promise<{ proposals: Proposal[]; total: number }> {
    const response = await apiClient.get(this.BASE_PATH, { params: filters })
    return response.data
  }

  static async getProposal(id: string): Promise<Proposal> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}`)
    return response.data
  }

  static async createProposal(data: Partial<Proposal>): Promise<Proposal> {
    const response = await apiClient.post(this.BASE_PATH, data)
    return response.data
  }

  static async updateProposal(id: string, data: Partial<Proposal>): Promise<Proposal> {
    const response = await apiClient.patch(`${this.BASE_PATH}/${id}`, data)
    return response.data
  }

  static async deleteProposal(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${id}`)
  }

  // Workflow operations
  static async submitProposal(id: string): Promise<Proposal> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/submit`)
    return response.data
  }

  static async approveProposal(id: string, notes?: string): Promise<Proposal> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/approve`, { notes })
    return response.data
  }

  static async rejectProposal(id: string, reason: string): Promise<Proposal> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/reject`, { reason })
    return response.data
  }

  static async requestRevision(id: string, comments: string): Promise<Proposal> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/request-revision`, { comments })
    return response.data
  }

  // Documents
  static async uploadAttachment(proposalId: string, file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await apiClient.post(`${this.BASE_PATH}/${proposalId}/attachments`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  static async removeAttachment(proposalId: string, attachmentId: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${proposalId}/attachments/${attachmentId}`)
  }

  // Analytics
  static async getProposalAnalytics(): Promise<any> {
    const response = await apiClient.get(`${this.BASE_PATH}/analytics`)
    return response.data
  }
}