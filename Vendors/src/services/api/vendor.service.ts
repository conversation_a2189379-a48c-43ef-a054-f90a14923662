import { apiClient } from '@luminar/shared-auth'

export interface Vendor {
  id: string
  name: string
  email: string
  phone: string
  website?: string
  category: string
  status: 'active' | 'inactive' | 'pending'
  rating: number
  description?: string
  specialties: string[]
  certifications: string[]
  contractStart?: string
  contractEnd?: string
  totalSpend: number
  activeProjects: number
  createdAt: string
  updatedAt: string
}

export interface VendorFilters {
  category?: string
  status?: string
  minRating?: number
  search?: string
  page?: number
  limit?: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface VendorStats {
  totalVendors: number
  activeVendors: number
  pendingApprovals: number
  averageRating: number
  totalSpend: number
  topCategories: Array<{ category: string; count: number }>
}

export class VendorService {
  private static BASE_PATH = '/api/v1/vendors'

  // Vendor CRUD operations
  static async listVendors(filters?: VendorFilters): Promise<{ vendors: Vendor[]; total: number }> {
    const response = await apiClient.get(this.BASE_PATH, { params: filters })
    return response.data
  }

  static async getVendor(id: string): Promise<Vendor> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}`)
    return response.data
  }

  static async createVendor(data: Partial<Vendor>): Promise<Vendor> {
    const response = await apiClient.post(this.BASE_PATH, data)
    return response.data
  }

  static async updateVendor(id: string, data: Partial<Vendor>): Promise<Vendor> {
    const response = await apiClient.patch(`${this.BASE_PATH}/${id}`, data)
    return response.data
  }

  static async deleteVendor(id: string): Promise<void> {
    await apiClient.delete(`${this.BASE_PATH}/${id}`)
  }

  // Vendor operations
  static async activateVendor(id: string): Promise<Vendor> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/activate`)
    return response.data
  }

  static async deactivateVendor(id: string): Promise<Vendor> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/deactivate`)
    return response.data
  }

  static async approveVendor(id: string): Promise<Vendor> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/approve`)
    return response.data
  }

  static async rejectVendor(id: string, reason: string): Promise<Vendor> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/reject`, { reason })
    return response.data
  }

  // Analytics
  static async getVendorStats(): Promise<VendorStats> {
    const response = await apiClient.get(`${this.BASE_PATH}/analytics/stats`)
    return response.data
  }

  static async getVendorPerformance(id: string): Promise<any> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/performance`)
    return response.data
  }

  static async getVendorSpendAnalysis(): Promise<any> {
    const response = await apiClient.get(`${this.BASE_PATH}/analytics/spend`)
    return response.data
  }

  // Reviews
  static async getVendorReviews(id: string): Promise<any> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/reviews`)
    return response.data
  }

  static async addVendorReview(id: string, review: any): Promise<any> {
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/reviews`, review)
    return response.data
  }

  // Documents
  static async getVendorDocuments(id: string): Promise<any> {
    const response = await apiClient.get(`${this.BASE_PATH}/${id}/documents`)
    return response.data
  }

  static async uploadVendorDocument(id: string, file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await apiClient.post(`${this.BASE_PATH}/${id}/documents`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  // Bulk operations
  static async bulkImportVendors(file: File): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await apiClient.post(`${this.BASE_PATH}/bulk/import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  }

  static async exportVendors(filters?: VendorFilters): Promise<Blob> {
    const response = await apiClient.get(`${this.BASE_PATH}/export`, {
      params: filters,
      responseType: 'blob'
    })
    return response.data
  }
}