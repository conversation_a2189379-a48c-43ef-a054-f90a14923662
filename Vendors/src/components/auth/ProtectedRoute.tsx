import { Navigate } from '@tanstack/react-router'
import { useAuth } from '~/contexts/AuthContext'
import { LoadingSkeleton } from '~/components/ui/LoadingSkeleton'

interface ProtectedRouteProps {
  children: React.ReactNode
  requiredRole?: string
}

export function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth()

  if (isLoading) {
    return <LoadingSkeleton type="page" />
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" />
  }

  // Check role if required
  if (requiredRole && user?.roles) {
    const hasRole = user.roles.some(role => role.name === requiredRole)
    if (!hasRole) {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
            <p className="text-muted-foreground">You don't have permission to view this page.</p>
          </div>
        </div>
      )
    }
  }

  return <>{children}</>
}