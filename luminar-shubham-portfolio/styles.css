/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-purple: #8B5CF6;
    --primary-cyan: #06B6D4;
    --accent-pink: #EC4899;
    --accent-orange: #F59E0B;
    --dark-bg: #0F0F23;
    --darker-bg: #0A0A1A;
    --card-bg: rgba(139, 92, 246, 0.1);
    --text-primary: #FFFFFF;
    --text-secondary: #A1A1AA;
    --border-color: rgba(139, 92, 246, 0.3);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--dark-bg);
    color: var(--text-primary);
    overflow-x: hidden;
    line-height: 1.6;
}

/* Loading screen */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--darker-bg);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    transition: opacity 0.5s ease;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 3px solid rgba(139, 92, 246, 0.3);
    border-top: 3px solid var(--primary-purple);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    color: var(--primary-purple);
    font-size: 1.1rem;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Background canvas */
#background-canvas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    opacity: 0.6;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    padding: 1.5rem 2rem;
    background: rgba(15, 15, 35, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-cyan));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 800;
    font-size: 1.2rem;
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    color: var(--primary-purple);
}

.nav-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-purple), var(--primary-cyan));
    transition: width 0.3s ease;
}

.nav-links a:hover::after {
    width: 100%;
}

/* Hero section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    position: relative;
}

.hero-content {
    max-width: 1200px;
    width: 100%;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: clamp(3rem, 8vw, 5rem);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.highlight {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-cyan));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    line-height: 1.6;
    max-width: 500px;
}

/* Code editor mockup */
.code-editor-mockup {
    background: rgba(10, 10, 26, 0.8);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    overflow: hidden;
    backdrop-filter: blur(20px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
}

.editor-header {
    background: rgba(139, 92, 246, 0.1);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.editor-controls {
    display: flex;
    gap: 0.5rem;
}

.control {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.control.close { background: #FF5F57; }
.control.minimize { background: #FFBD2E; }
.control.maximize { background: #28CA42; }

.editor-title {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.editor-content {
    display: flex;
    padding: 1.5rem 0;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9rem;
    line-height: 1.6;
}

.line-numbers {
    padding: 0 1rem;
    color: var(--text-secondary);
    opacity: 0.5;
    user-select: none;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--border-color);
    margin-right: 1rem;
}

.code-text {
    flex: 1;
    padding-right: 1.5rem;
}

.code-line {
    margin-bottom: 0.2rem;
}

.keyword { color: #FF79C6; }
.variable { color: #8BE9FD; }
.property { color: #50FA7B; }
.string { color: #F1FA8C; }

/* Floating elements */
.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.floating-dot {
    position: absolute;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

.dot-1 {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-cyan));
    top: 20%;
    right: 10%;
    animation-delay: 0s;
}

.dot-2 {
    background: linear-gradient(135deg, var(--accent-pink), var(--accent-orange));
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.dot-3 {
    background: linear-gradient(135deg, var(--primary-cyan), var(--accent-pink));
    bottom: 30%;
    left: 15%;
    animation-delay: 4s;
}

.dot-4 {
    background: linear-gradient(135deg, var(--accent-orange), var(--primary-purple));
    top: 40%;
    left: 10%;
    animation-delay: 1s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

/* Scroll indicator */
.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.scroll-line {
    width: 2px;
    height: 60px;
    background: linear-gradient(to bottom, var(--primary-purple), transparent);
}

.scroll-dot {
    width: 8px;
    height: 8px;
    background: var(--primary-purple);
    border-radius: 50%;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Container */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Section styles */
section {
    padding: 6rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-label {
    color: var(--primary-purple);
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-bottom: 1rem;
    display: block;
}

.section-title {
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 800;
    margin-bottom: 2rem;
}

/* Overview section */
.overview-section {
    background: rgba(139, 92, 246, 0.05);
    border-top: 1px solid var(--border-color);
    border-bottom: 1px solid var(--border-color);
}

.overview-text {
    font-size: 1.2rem;
    color: var(--text-secondary);
    line-height: 1.8;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

/* Applications section */
.applications-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.app-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(20px);
}

.app-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-purple);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}

.app-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
}

.geometric-icon {
    width: 100%;
    height: 100%;
}

.app-card h3 {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--primary-purple);
}

.app-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Features section */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-item {
    padding: 2rem;
    border-left: 3px solid var(--primary-purple);
    background: rgba(139, 92, 246, 0.05);
    border-radius: 0 12px 12px 0;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(139, 92, 246, 0.1);
    transform: translateX(10px);
}

.feature-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--primary-purple);
    margin-bottom: 1rem;
}

.feature-item h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: 1rem;
}

.feature-item p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Contact section */
.contact-section {
    background: rgba(139, 92, 246, 0.05);
    border-top: 1px solid var(--border-color);
}

.contact-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.contact-text {
    font-size: 1.2rem;
    color: var(--text-secondary);
    line-height: 1.8;
    margin-bottom: 3rem;
}

.contact-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-purple), var(--primary-cyan));
    color: white;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-purple);
    border: 2px solid var(--primary-purple);
}

.btn-secondary:hover {
    background: var(--primary-purple);
    color: white;
    transform: translateY(-3px);
}

/* Footer */
.footer {
    background: var(--darker-bg);
    border-top: 1px solid var(--border-color);
    padding: 3rem 0 2rem;
}

.footer-content {
    text-align: center;
    margin-bottom: 2rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-text {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links a:hover {
    color: var(--primary-purple);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

.footer-bottom p {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Fade-in animation */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive design */
@media (max-width: 768px) {
    .navbar {
        padding: 1rem;
    }
    
    .nav-links {
        display: none;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .code-editor-mockup {
        order: -1;
    }
    
    .applications-grid {
        grid-template-columns: 1fr;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
    
    .contact-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .footer-links {
        flex-direction: column;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 1rem;
    }
    
    section {
        padding: 4rem 0;
    }
    
    .hero-text h1 {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .app-card {
        padding: 2rem;
    }
    
    .feature-item {
        padding: 1.5rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--darker-bg);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, var(--primary-purple), var(--primary-cyan));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, var(--primary-cyan), var(--accent-pink));
}

/* Selection styling */
::selection {
    background: rgba(139, 92, 246, 0.3);
    color: white;
}

::-moz-selection {
    background: rgba(139, 92, 246, 0.3);
    color: white;
}